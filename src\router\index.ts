import { createRouter, createWebHistory } from 'vue-router'
import StartScreen from '@/views/StartScreen.vue'
import MainScene from '@/views/MainScene.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'start',
      component: StartScreen,
      meta: {
        title: '琼浆玉液 - 开始游戏'
      }
    },
    {
      path: '/main',
      name: 'main',
      component: MainScene,
      meta: {
        title: '琼浆玉液 - 主场景'
      }
    },
    {
      path: '/llyzf',
      name: 'llyzf',
      component: () => import('@/views/LLYZF.vue'),
      meta: {
        title: '琉璃胭脂坊'
      }
    },
    {
      path: '/workshop/:type',
      name: 'workshop',
      component: () => import('@/views/Workshop.vue'),
      meta: {
        title: '工坊'
      }
    },
    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      redirect: '/'
    }
  ],
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }

  next()
})

export default router
