<template>
  <div class="start-screen">
    <div class="background-overlay"></div>
    <div class="content-container">
      <!-- 游戏标题 -->
      <div class="game-title">
        <h1 class="title-text">琼浆玉液</h1>
        <p class="subtitle-text">经营你的琉璃胭脂坊</p>
      </div>
      
      <!-- 主菜单按钮 -->
      <div class="menu-buttons">
        <a-button 
          type="primary" 
          size="large" 
          class="menu-btn new-game-btn"
          @click="startNewGame"
        >
          <template #icon>
            <PlusOutlined />
          </template>
          新游戏
        </a-button>
        
        <a-button 
          size="large" 
          class="menu-btn load-game-btn"
          @click="loadGame"
          :disabled="!hasSaveData"
        >
          <template #icon>
            <FolderOpenOutlined />
          </template>
          读取存档
        </a-button>
        
        <a-button 
          size="large" 
          class="menu-btn settings-btn"
          @click="openSettings"
        >
          <template #icon>
            <SettingOutlined />
          </template>
          设置
        </a-button>
      </div>
      
      <!-- 版本信息 -->
      <div class="version-info">
        <span>版本 1.0.0 MVP</span>
      </div>
    </div>
    
    <!-- 设置弹窗 -->
    <a-modal
      v-model:open="settingsVisible"
      title="游戏设置"
      :footer="null"
      width="600px"
      centered
    >
      <SettingsPanel @close="settingsVisible = false" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { PlusOutlined, FolderOpenOutlined, SettingOutlined } from '@ant-design/icons-vue'
import SettingsPanel from '@/components/SettingsPanel.vue'

const router = useRouter()
const settingsVisible = ref(false)

// 检查是否有存档数据
const hasSaveData = computed(() => {
  return localStorage.getItem('nectar-game-save') !== null
})

// 开始新游戏
const startNewGame = () => {
  // 如果有存档，询问是否覆盖
  if (hasSaveData.value) {
    // 这里可以添加确认对话框
    message.warning('检测到已有存档，新游戏将覆盖现有进度')
  }
  
  // 清除旧存档
  localStorage.removeItem('nectar-game-save')
  
  // 跳转到主游戏场景
  router.push('/main')
}

// 读取存档
const loadGame = () => {
  if (!hasSaveData.value) {
    message.error('没有找到存档文件')
    return
  }
  
  message.success('存档加载成功')
  router.push('/main')
}

// 打开设置
const openSettings = () => {
  settingsVisible.value = true
}

onMounted(() => {
  // 页面加载时的初始化逻辑
})
</script>

<style scoped>
.start-screen {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('@/assets/graphics/bg-pattern.png') repeat;
  opacity: 0.1;
  z-index: 1;
}

.content-container {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.game-title {
  margin-bottom: 40px;
}

.title-text {
  font-size: 48px;
  font-weight: bold;
  background: linear-gradient(135deg, var(--nectar-gold), var(--nectar-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle-text {
  font-size: 18px;
  color: #666;
  margin: 10px 0 0 0;
  font-weight: 500;
}

.menu-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 30px;
}

.menu-btn {
  height: 56px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  border: none;
}

.new-game-btn {
  background: linear-gradient(135deg, var(--nectar-purple), var(--nectar-pink));
}

.new-game-btn:hover {
  background: linear-gradient(135deg, var(--nectar-pink), var(--nectar-purple));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(114, 46, 209, 0.3);
}

.load-game-btn {
  background: linear-gradient(135deg, var(--nectar-blue), var(--nectar-purple));
  color: white;
}

.load-game-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--nectar-purple), var(--nectar-blue));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.load-game-btn:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.settings-btn {
  background: linear-gradient(135deg, var(--nectar-gold), var(--nectar-purple));
  color: white;
}

.settings-btn:hover {
  background: linear-gradient(135deg, var(--nectar-purple), var(--nectar-gold));
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
}

.version-info {
  color: #999;
  font-size: 12px;
  margin-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content-container {
    max-width: 90%;
    padding: 30px 20px;
  }
  
  .title-text {
    font-size: 36px;
  }
  
  .subtitle-text {
    font-size: 16px;
  }
  
  .menu-btn {
    height: 48px;
    font-size: 14px;
  }
}
</style>
