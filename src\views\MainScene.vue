<template>
  <div class="main-scene">
    <!-- 顶部状态栏 - 使用Affix固定 -->
    <a-affix :offset-top="0">
      <div class="top-status-bar">
        <!-- 左侧：时间显示 -->
        <div class="status-left">
          <div class="time-display" :class="{ compact: isMobile }" @click="isMobile && toggleTimeDetail()">
            <ClockCircleOutlined />
            <span v-if="!isMobile || showTimeDetail">{{ gameStore.fullTimeDisplay }}</span>
            <span v-else>第{{ gameStore.currentDay }}天</span>
          </div>
        </div>

        <!-- 右侧：资源显示 -->
        <div class="status-right">
          <div class="resource-display" :class="{ compact: isMobile }" @click="isMobile && toggleResourceDetail()">
            <a-space :size="isMobile ? 'small' : 'large'">
              <div class="resource-item" v-if="!isMobile || showResourceDetail">
                <span class="resource-icon">💰</span>
                <span class="resource-value">{{ gameStore.playerResources.money }}</span>
              </div>
              <div class="resource-item" v-if="!isMobile || showResourceDetail">
                <span class="resource-icon">💎</span>
                <span class="resource-value">{{ gameStore.playerResources.gems }}</span>
              </div>
              <div class="resource-item" v-if="!isMobile || showResourceDetail">
                <span class="resource-icon">⚡</span>
                <span class="resource-value">{{ gameStore.playerResources.energy }}/{{
                  gameStore.playerResources.maxEnergy }}</span>
              </div>
              <!-- 移动端简化显示 -->
              <div class="resource-item" v-if="isMobile && !showResourceDetail">
                <span class="resource-icon">💰</span>
                <span class="resource-value">{{ gameStore.playerResources.money }}</span>
                <MoreOutlined class="more-icon" />
              </div>
            </a-space>
          </div>
        </div>
      </div>
    </a-affix>

    <!-- 主内容区域 -->
    <div class="game-content">
      <div class="content-wrapper">
        <!-- 根据当前选择的菜单显示不同内容 -->
        <component :is="currentComponent" />
      </div>
    </div>

    <!-- 底部导航栏 - 使用Affix固定 -->
    <a-affix :offset-bottom="0">
      <div class="bottom-navigation">
        <!-- 桌面端：显示所有按钮 -->
        <div v-if="!isMobile" class="nav-buttons-desktop">
          <a-space size="large">
            <a-button v-for="item in navigationItems" :key="item.key"
              :type="selectedKeys[0] === item.key ? 'primary' : 'default'" @click="handleNavClick(item.key)"
              class="nav-button">
              <template #icon>
                <component :is="item.icon" />
              </template>
              {{ item.label }}
            </a-button>
          </a-space>
        </div>

        <!-- 移动端：收纳按钮 -->
        <div v-else class="nav-buttons-mobile">
          <!-- 收纳的导航菜单 -->
          <div v-if="showMobileNav" class="mobile-nav-panel">
            <div class="nav-grid">
              <div v-for="item in navigationItems" :key="item.key" class="nav-item"
                :class="{ active: selectedKeys[0] === item.key }" @click="handleNavClick(item.key)">
                <component :is="item.icon" class="nav-icon" />
                <span class="nav-label">{{ item.label }}</span>
              </div>
            </div>
          </div>

          <!-- 收纳按钮 -->
          <a-button type="primary" shape="circle" size="large" class="mobile-nav-toggle" @click="toggleMobileNav">
            <MenuOutlined v-if="!showMobileNav" />
            <CloseOutlined v-else />
          </a-button>
        </div>
      </div>
    </a-affix>

    <!-- 设置弹窗 -->
    <a-modal v-model:open="settingsVisible" title="游戏设置" :footer="null" width="600px" centered>
      <SettingsPanel @close="settingsVisible = false" />
    </a-modal>

    <!-- 退出确认弹窗 -->
    <a-modal v-model:open="exitConfirmVisible" title="退出游戏" centered @ok="confirmExit"
      @cancel="exitConfirmVisible = false">
      <p>确定要退出游戏吗？未保存的进度将会丢失。</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  EnvironmentOutlined,
  TeamOutlined,
  ShoppingOutlined,
  CalendarOutlined,
  BranchesOutlined,
  SettingOutlined,
  LogoutOutlined,
  ClockCircleOutlined,
  MoreOutlined,
  MenuOutlined,
  CloseOutlined,
} from '@ant-design/icons-vue'

// 组件导入
import SettingsPanel from '@/components/SettingsPanel.vue'
import GameMap from '@/components/GameMap.vue'
import { useGameStore } from '@/stores/gameStore'

// 临时占位组件
const PlaceholderComponent = {
  template: '<div class="placeholder">该功能正在开发中...</div>'
}

const router = useRouter()
const gameStore = useGameStore()

// 响应式数据
const selectedKeys = ref(['map'])
const settingsVisible = ref(false)
const exitConfirmVisible = ref(false)

// 移动端相关状态
const isMobile = ref(false)
const showMobileNav = ref(false)
const showTimeDetail = ref(false)
const showResourceDetail = ref(false)

// 导航项配置
const navigationItems = [
  { key: 'map', label: '游戏地图', icon: EnvironmentOutlined },
  { key: 'characters', label: '角色列表', icon: TeamOutlined },
  { key: 'inventory', label: '库存', icon: ShoppingOutlined },
  { key: 'calendar', label: '日历', icon: CalendarOutlined },
  { key: 'development', label: '发展树', icon: BranchesOutlined },
  { key: 'settings', label: '设置', icon: SettingOutlined },
  { key: 'exit', label: '退出游戏', icon: LogoutOutlined },
]

// 组件映射
const componentMap = {
  map: GameMap,
  characters: PlaceholderComponent,
  inventory: PlaceholderComponent,
  calendar: PlaceholderComponent,
  development: PlaceholderComponent,
}

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap[selectedKeys.value[0] as keyof typeof componentMap] || GameMap
})

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 切换移动端导航
const toggleMobileNav = () => {
  showMobileNav.value = !showMobileNav.value
}

// 切换时间详情显示
const toggleTimeDetail = () => {
  showTimeDetail.value = !showTimeDetail.value
  setTimeout(() => {
    showTimeDetail.value = false
  }, 3000)
}

// 切换资源详情显示
const toggleResourceDetail = () => {
  showResourceDetail.value = !showResourceDetail.value
  setTimeout(() => {
    showResourceDetail.value = false
  }, 3000)
}

// 导航点击处理
const handleNavClick = (key: string) => {
  if (key === 'settings') {
    settingsVisible.value = true
    showMobileNav.value = false
    return
  }

  if (key === 'exit') {
    exitConfirmVisible.value = true
    showMobileNav.value = false
    return
  }

  selectedKeys.value = [key]
  showMobileNav.value = false
}

// 确认退出
const confirmExit = () => {
  // 保存游戏
  gameStore.saveGame()
  message.success('游戏已保存')
  router.push('/')
}

onMounted(() => {
  // 尝试加载游戏数据，如果没有存档则使用默认状态
  const hasData = gameStore.loadGame()
  if (hasData) {
    message.success(`欢迎回来！当前进度：${gameStore.fullTimeDisplay}`)
  } else {
    message.success('欢迎来到琼浆玉液的世界！')
  }

  // 检测移动端
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.main-scene {
  height: 100vh;
  background: var(--bg-primary);
}

/* 顶部状态栏 */
.top-status-bar {
  background: white;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, var(--nectar-blue), var(--nectar-purple));
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-display:hover {
  transform: scale(1.05);
}

.time-display.compact {
  padding: 6px 12px;
  font-size: 14px;
}

.resource-display {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resource-display:hover {
  background: rgba(0, 0, 0, 0.1);
}

.resource-display.compact {
  padding: 6px 12px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.resource-icon {
  font-size: 16px;
}

.resource-value {
  font-weight: bold;
  color: #333;
}

.more-icon {
  margin-left: 4px;
  color: #999;
}

/* 主内容区域 */
.game-content {
  background: var(--bg-primary);
  min-height: calc(100vh - 120px);
  padding-top: 20px;
  padding-bottom: 80px;
}

.content-wrapper {
  padding: 0 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 12px;
  font-size: 18px;
  color: #999;
}

/* 底部导航栏 */
.bottom-navigation {
  background: white;
  padding: 16px 20px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-buttons-desktop {
  display: flex;
  justify-content: center;
}

.nav-button {
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-button:hover {
  transform: translateY(-2px);
}

/* 移动端导航 */
.nav-buttons-mobile {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.mobile-nav-panel {
  position: absolute;
  bottom: 70px;
  right: 0;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 280px;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.nav-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.nav-item.active {
  background: var(--nectar-purple);
  color: white;
}

.nav-icon {
  font-size: 24px;
}

.nav-label {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.mobile-nav-toggle {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .top-status-bar {
    padding: 8px 16px;
  }

  .content-wrapper {
    padding: 0 16px;
  }

  .bottom-navigation {
    padding: 12px 16px;
  }

  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .mobile-nav-panel {
    right: 16px;
    min-width: 240px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .time-display {
    padding: 4px 8px;
    font-size: 12px;
  }

  .resource-display {
    padding: 4px 8px;
  }

  .resource-icon {
    font-size: 14px;
  }

  .resource-value {
    font-size: 12px;
  }
}
</style>
