<template>
  <a-layout class="main-scene">
    <!-- 侧边导航栏 -->
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible :width="240" :collapsed-width="80"
      class="game-sider">
      <div class="logo-container">
        <div class="logo">
          <span v-if="!collapsed" class="logo-text">琼浆玉液</span>
          <span v-else class="logo-icon">琼</span>
        </div>
      </div>

      <a-menu v-model:selectedKeys="selectedKeys" mode="inline" theme="dark" class="game-menu" @click="handleMenuClick">
        <a-menu-item key="map">
          <template #icon>
            <EnvironmentOutlined />
          </template>
          <span>游戏地图</span>
        </a-menu-item>

        <a-menu-item key="characters">
          <template #icon>
            <TeamOutlined />
          </template>
          <span>角色列表</span>
        </a-menu-item>

        <a-menu-item key="inventory">
          <template #icon>
            <ShoppingOutlined />
          </template>
          <span>库存</span>
        </a-menu-item>

        <a-menu-item key="calendar">
          <template #icon>
            <CalendarOutlined />
          </template>
          <span>日历</span>
        </a-menu-item>

        <a-menu-item key="development">
          <template #icon>
            <BranchesOutlined />
          </template>
          <span>发展树</span>
        </a-menu-item>

        <a-menu-item key="settings">
          <template #icon>
            <SettingOutlined />
          </template>
          <span>设置</span>
        </a-menu-item>

        <a-menu-item key="exit" class="exit-menu">
          <template #icon>
            <LogoutOutlined />
          </template>
          <span>退出游戏</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <!-- 主内容区域 -->
    <a-layout>
      <!-- 顶部状态栏 -->
      <a-layout-header class="game-header">
        <div class="header-left">
          <a-button type="text" class="trigger-btn" @click="collapsed = !collapsed">
            <MenuUnfoldOutlined v-if="collapsed" />
            <MenuFoldOutlined v-else />
          </a-button>

          <!-- 时间显示 -->
          <div class="time-display">
            <ClockCircleOutlined />
            <span>{{ gameStore.fullTimeDisplay }}</span>
          </div>
        </div>

        <div class="header-right">
          <!-- 资源显示 -->
          <div class="resource-display">
            <a-space size="large">
              <div class="resource-item">
                <span class="resource-icon">💰</span>
                <span class="resource-value">{{ gameStore.playerResources.money }}</span>
              </div>
              <div class="resource-item">
                <span class="resource-icon">💎</span>
                <span class="resource-value">{{ gameStore.playerResources.gems }}</span>
              </div>
              <div class="resource-item">
                <span class="resource-icon">⚡</span>
                <span class="resource-value">{{ gameStore.playerResources.energy }}/{{
                  gameStore.playerResources.maxEnergy
                }}</span>
              </div>
            </a-space>
          </div>
        </div>
      </a-layout-header>

      <!-- 主内容 -->
      <a-layout-content class="game-content">
        <div class="content-wrapper">
          <!-- 根据当前选择的菜单显示不同内容 -->
          <component :is="currentComponent" />
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 设置弹窗 -->
    <a-modal v-model:open="settingsVisible" title="游戏设置" :footer="null" width="600px" centered>
      <SettingsPanel @close="settingsVisible = false" />
    </a-modal>

    <!-- 退出确认弹窗 -->
    <a-modal v-model:open="exitConfirmVisible" title="退出游戏" centered @ok="confirmExit"
      @cancel="exitConfirmVisible = false">
      <p>确定要退出游戏吗？未保存的进度将会丢失。</p>
    </a-modal>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  EnvironmentOutlined,
  TeamOutlined,
  ShoppingOutlined,
  CalendarOutlined,
  BranchesOutlined,
  SettingOutlined,
  LogoutOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons-vue'

// 组件导入
import SettingsPanel from '@/components/SettingsPanel.vue'
import GameMap from '@/components/GameMap.vue'
import { useGameStore } from '@/stores/gameStore'

// 临时占位组件
const PlaceholderComponent = {
  template: '<div class="placeholder">该功能正在开发中...</div>'
}

const router = useRouter()
const gameStore = useGameStore()

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref(['map'])
const settingsVisible = ref(false)
const exitConfirmVisible = ref(false)

// 组件映射
const componentMap = {
  map: GameMap,
  characters: PlaceholderComponent, // CharacterList,
  inventory: PlaceholderComponent, // Inventory,
  calendar: PlaceholderComponent, // Calendar,
  development: PlaceholderComponent, // DevelopmentTree,
}

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap[selectedKeys.value[0] as keyof typeof componentMap] || GameMap
})

// 菜单点击处理
const handleMenuClick = ({ key }: { key: string }) => {
  if (key === 'settings') {
    settingsVisible.value = true
    return
  }

  if (key === 'exit') {
    exitConfirmVisible.value = true
    return
  }

  selectedKeys.value = [key]
}

// 确认退出
const confirmExit = () => {
  // 保存游戏
  gameStore.saveGame()
  message.success('游戏已保存')
  router.push('/')
}

onMounted(() => {
  // 尝试加载游戏数据，如果没有存档则使用默认状态
  const hasData = gameStore.loadGame()
  if (hasData) {
    message.success(`欢迎回来！当前进度：${gameStore.fullTimeDisplay}`)
  } else {
    message.success('欢迎来到琼浆玉液的世界！')
  }
})
</script>

<style scoped>
.main-scene {
  height: 100vh;
}

.game-sider {
  background: #001529;
}

.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
}

.logo-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.logo-icon {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.game-menu {
  border-right: none;
}

.exit-menu {
  margin-top: auto;
  border-top: 1px solid #303030;
}

.game-header {
  background: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.trigger-btn {
  font-size: 18px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, var(--nectar-blue), var(--nectar-purple));
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
}

.resource-display {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px 16px;
  border-radius: 12px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.resource-icon {
  font-size: 16px;
}

.resource-value {
  font-weight: bold;
  color: #333;
}

.game-content {
  background: var(--bg-primary);
  overflow: auto;
}

.content-wrapper {
  padding: 24px;
  height: 100%;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 12px;
  font-size: 18px;
  color: #999;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .game-header {
    padding: 0 16px;
  }

  .header-left {
    gap: 12px;
  }

  .resource-display {
    display: none;
  }

  .content-wrapper {
    padding: 16px;
  }
}
</style>
