<template>
  <div class="character-details">
    <!-- 基础信息 -->
    <div class="details-section">
      <h4>基础信息</h4>
      <a-descriptions :column="2" size="small">
        <a-descriptions-item label="姓名">{{ character.name }}</a-descriptions-item>
        <a-descriptions-item label="类型">{{ getTypeText(character.type) }}</a-descriptions-item>
        <a-descriptions-item label="等级">Lv.{{ character.level }}</a-descriptions-item>
        <a-descriptions-item label="经验">{{ character.experience }}/100</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(character.status)">
            {{ getStatusText(character.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="连续工作">{{ character.restDays }}天</a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 属性详情 -->
    <div class="details-section">
      <h4>属性详情</h4>
      <div class="attribute-grid">
        <div class="attribute-item">
          <div class="attribute-label">体力值</div>
          <div class="attribute-bar">
            <a-progress 
              :percent="character.stamina" 
              :stroke-color="getStaminaColor(character.stamina)"
              :show-info="false"
              size="small"
            />
            <span class="attribute-value">{{ character.stamina }}/100</span>
          </div>
        </div>
        
        <div class="attribute-item">
          <div class="attribute-label">心情值</div>
          <div class="attribute-bar">
            <a-progress 
              :percent="character.mood" 
              :stroke-color="getMoodColor(character.mood)"
              :show-info="false"
              size="small"
            />
            <span class="attribute-value">{{ character.mood }}/100</span>
          </div>
        </div>
        
        <div class="attribute-item">
          <div class="attribute-label">能量值</div>
          <div class="attribute-bar">
            <a-progress 
              :percent="(character.attributes.energy / character.attributes.maxEnergy) * 100" 
              stroke-color="#52c41a"
              :show-info="false"
              size="small"
            />
            <span class="attribute-value">{{ character.attributes.energy }}/{{ character.attributes.maxEnergy }}</span>
          </div>
        </div>
        
        <div class="attribute-item">
          <div class="attribute-label">生产效率</div>
          <div class="attribute-value-only">{{ character.attributes.efficiency }}x</div>
        </div>
      </div>
    </div>

    <!-- 技能详情 -->
    <div class="details-section" v-if="character.attributes.skills.length > 0">
      <h4>技能详情</h4>
      <div class="skills-list">
        <div 
          v-for="skillId in character.attributes.skills" 
          :key="skillId"
          class="skill-item"
        >
          <div class="skill-header">
            <a-tag :color="getSkillColor(skillId)" class="skill-tag">
              {{ getSkillName(skillId) }}
            </a-tag>
            <span class="skill-rarity">{{ getSkillRarity(skillId) }}</span>
          </div>
          <div class="skill-description">{{ getSkillDescription(skillId) }}</div>
          <div class="skill-effects">
            <div 
              v-for="effect in getSkillEffects(skillId)" 
              :key="effect.description"
              class="skill-effect"
            >
              • {{ effect.description }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态说明 -->
    <div class="details-section">
      <h4>状态说明</h4>
      <div class="status-info">
        <div class="status-item">
          <a-tag color="green">正常</a-tag>
          <span>体力 & 心情 > 50，连续休息</span>
        </div>
        <div class="status-item">
          <a-tag color="orange">疲惫</a-tag>
          <span>连续派遣2天未休息 or 心情或体力低于50</span>
        </div>
        <div class="status-item">
          <a-tag color="red">过劳</a-tag>
          <span>连续派遣3天以上 or 心情/体力低于30</span>
        </div>
        <div class="status-item">
          <a-tag color="blue">休息</a-tag>
          <span>回归宗门修养恢复</span>
        </div>
        <div class="status-item">
          <a-tag color="default">流放</a-tag>
          <span>连续过劳或未修养超过3天</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Character } from '@/stores/characterStore'
import { useSkillStore } from '@/stores/skillStore'

interface Props {
  character: Character
}

const props = defineProps<Props>()
const skillStore = useSkillStore()

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'Normal': '正常',
    'Tired': '疲惫',
    'Overworked': '过劳',
    'Resting': '休息',
    'Exiled': '流放'
  }
  return statusMap[status] || status
}

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'Normal': 'green',
    'Tired': 'orange',
    'Overworked': 'red',
    'Resting': 'blue',
    'Exiled': 'default'
  }
  return colorMap[status] || 'default'
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return typeMap[type] || type
}

// 获取体力颜色
const getStaminaColor = (stamina: number): string => {
  if (stamina >= 70) return '#52c41a'
  if (stamina >= 40) return '#faad14'
  return '#ff4d4f'
}

// 获取心情颜色
const getMoodColor = (mood: number): string => {
  if (mood >= 70) return '#1890ff'
  if (mood >= 40) return '#faad14'
  return '#ff4d4f'
}

// 获取技能名称
const getSkillName = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.name : skillId
}

// 获取技能稀有度
const getSkillRarity = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? (skill.rarity === 'Rare' ? '稀有' : '常见') : '未知'
}

// 获取技能描述
const getSkillDescription = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.description : '未知技能'
}

// 获取技能效果
const getSkillEffects = (skillId: string) => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.effects : []
}

// 获取技能颜色
const getSkillColor = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  if (!skill) return 'default'
  return skill.rarity === 'Rare' ? 'purple' : 'blue'
}
</script>

<style scoped>
.character-details {
  max-height: 600px;
  overflow-y: auto;
}

.details-section {
  margin-bottom: 24px;
}

.details-section h4 {
  margin-bottom: 12px;
  color: #333;
  font-weight: bold;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.attribute-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.attribute-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attribute-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.attribute-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attribute-bar .ant-progress {
  flex: 1;
}

.attribute-value {
  font-size: 12px;
  color: #333;
  font-weight: bold;
  min-width: 50px;
  text-align: right;
}

.attribute-value-only {
  font-size: 14px;
  color: #333;
  font-weight: bold;
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skill-item {
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid var(--nectar-purple);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.skill-tag {
  font-weight: bold;
}

.skill-rarity {
  font-size: 12px;
  color: #666;
}

.skill-description {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.skill-effects {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skill-effect {
  font-size: 12px;
  color: #666;
  padding-left: 8px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.status-item span {
  color: #666;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .attribute-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .skill-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
