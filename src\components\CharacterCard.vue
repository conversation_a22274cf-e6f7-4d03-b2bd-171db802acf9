<template>
  <div class="character-card" :class="[
    `character-${character.type.toLowerCase()}`,
    `status-${character.status.toLowerCase()}`,
    {
      'card-selected': selected,
      'card-disabled': disabled,
      'card-assigned': character.attributes.isAssigned
    }
  ]" @click="handleCardClick">
    <!-- 角色头像区域 -->
    <div class="character-avatar">
      <a-avatar :src="`/src/assets/graphics/portraits/${character.portrait}`" :size="80" class="avatar-image">
        {{ character.name.charAt(0) }}
      </a-avatar>

      <!-- 等级标识 -->
      <div class="level-badge">
        <span>Lv.{{ character.level }}</span>
      </div>

      <!-- 状态标识 -->
      <div class="status-badge" :class="`status-${character.status.toLowerCase()}`">
        <span>{{ getStatusText(character.status) }}</span>
      </div>
    </div>

    <!-- 角色信息区域 -->
    <div class="character-info">
      <div class="character-name">{{ character.name }}</div>
      <div class="character-type">{{ getTypeText(character.type) }}</div>

      <!-- 属性条 -->
      <div class="character-stats">
        <div class="stat-bar">
          <div class="stat-label">
            <span>体力</span>
            <span class="stat-value">{{ character.stamina }}/100</span>
          </div>
          <div class="stat-progress">
            <div class="stat-fill stamina-fill" :style="{ width: `${character.stamina}%` }"></div>
          </div>
        </div>

        <div class="stat-bar">
          <div class="stat-label">
            <span>心情</span>
            <span class="stat-value">{{ character.mood }}/100</span>
          </div>
          <div class="stat-progress">
            <div class="stat-fill mood-fill" :style="{ width: `${character.mood}%` }"></div>
          </div>
        </div>

        <div class="stat-bar">
          <div class="stat-label">
            <span>经验</span>
            <span class="stat-value">{{ character.experience }}/100</span>
          </div>
          <div class="stat-progress">
            <div class="stat-fill exp-fill" :style="{ width: `${character.experience}%` }"></div>
          </div>
        </div>
      </div>

      <!-- 技能预览 -->
      <div class="character-skills" v-if="character.attributes.skills.length > 0">
        <div class="skills-header">
          <span>技能</span>
          <a-button type="text" size="small" @click.stop="showDetails = true" class="details-btn">
            详情
          </a-button>
        </div>
        <div class="skills-preview">
          <a-tag v-for="skillId in character.attributes.skills.slice(0, 2)" :key="skillId"
            :color="getSkillColor(skillId)" size="small">
            {{ getSkillName(skillId) }}
          </a-tag>
          <span v-if="character.attributes.skills.length > 2" class="more-skills">
            +{{ character.attributes.skills.length - 2 }}
          </span>
        </div>
      </div>
    </div>

    <!-- 选择指示器 -->
    <div v-if="selectable" class="selection-indicator">
      <div class="selection-circle" :class="{ active: selected }">
        <CheckOutlined v-if="selected" />
      </div>
    </div>

    <!-- 派遣状态指示器 -->
    <div v-if="character.attributes.isAssigned" class="assigned-indicator">
      <span>派遣中</span>
    </div>
  </div>

  <!-- 详情弹窗 -->
  <a-modal v-model:open="showDetails" :title="`${character.name} - 详细信息`" :footer="null" width="500px" centered>
    <CharacterDetails :character="character" />
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CheckOutlined } from '@ant-design/icons-vue'
import type { Character } from '@/stores/characterStore'
import { useSkillStore } from '@/stores/skillStore'
import CharacterDetails from './CharacterDetails.vue'

interface Props {
  character: Character
  selected?: boolean
  selectable?: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'select', character: Character): void
  (e: 'click', character: Character): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  selectable: false,
  disabled: false
})

const emit = defineEmits<Emits>()

const skillStore = useSkillStore()
const showDetails = ref(false)

// 处理卡片点击
const handleCardClick = () => {
  if (props.disabled) return

  if (props.selectable) {
    emit('select', props.character)
  } else {
    emit('click', props.character)
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'Normal': '正常',
    'Tired': '疲惫',
    'Overworked': '过劳',
    'Resting': '休息',
    'Exiled': '流放'
  }
  return statusMap[status] || status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return typeMap[type] || type
}

// 获取技能名称
const getSkillName = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.name : skillId
}

// 获取技能颜色
const getSkillColor = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  if (!skill) return 'default'
  return skill.rarity === 'Rare' ? 'purple' : 'blue'
}
</script>

<style scoped>
.character-card {
  position: relative;
  width: 280px;
  height: 360px;
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.character-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #d9d9d9;
}

/* 卡片类型样式 */
.character-normal {
  border-left: 4px solid #1890ff;
}

.character-rare {
  border-left: 4px solid #722ed1;
}

.character-special {
  border-left: 4px solid #eb2f96;
}

/* 状态样式 */
.status-tired {
  filter: saturate(0.7);
}

.status-overworked {
  filter: saturate(0.5) brightness(0.8);
}

.status-exiled {
  filter: grayscale(0.8);
  opacity: 0.7;
}

.card-selected {
  border-color: #1890ff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.card-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.card-assigned {
  border-color: #52c41a;
}

/* 头像区域 */
.character-avatar {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.avatar-image {
  border: 2px solid #f0f0f0;
}

.level-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #faad14;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
  color: white;
}

.status-normal {
  background: #52c41a;
}

.status-tired {
  background: #faad14;
}

.status-overworked {
  background: #ff4d4f;
}

.status-resting {
  background: #1890ff;
}

.status-exiled {
  background: #8c8c8c;
}

/* 角色信息 */
.character-info {
  text-align: center;
}

.character-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.character-type {
  font-size: 12px;
  color: #666;
  margin-bottom: 16px;
}

/* 属性条 */
.character-stats {
  margin-bottom: 16px;
}

.stat-bar {
  margin-bottom: 8px;
}

.stat-label {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-progress {
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.stamina-fill {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.mood-fill {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.exp-fill {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

/* 技能区域 */
.character-skills {
  text-align: left;
}

.skills-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.details-btn {
  padding: 0 4px;
  height: auto;
  font-size: 10px;
}

.skills-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-skills {
  font-size: 10px;
  color: #999;
}

/* 选择指示器 */
.selection-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
}

.selection-circle {
  width: 24px;
  height: 24px;
  border: 2px solid #d9d9d9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
}

.selection-circle.active {
  border-color: var(--nectar-purple);
  background: var(--nectar-purple);
  color: white;
}

/* 派遣指示器 */
.assigned-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .character-card {
    width: 240px;
    height: 320px;
    padding: 16px;
  }

  .avatar-frame {
    width: 60px;
    height: 60px;
  }

  .character-name {
    font-size: 16px;
  }
}
</style>
