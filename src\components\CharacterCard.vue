<template>
  <div 
    class="character-card" 
    :class="[
      `character-${character.type.toLowerCase()}`,
      `status-${character.status.toLowerCase()}`,
      { 
        'card-selected': selected,
        'card-disabled': disabled,
        'card-assigned': character.attributes.isAssigned
      }
    ]"
    @click="handleCardClick"
  >
    <!-- 卡片光效背景 -->
    <div class="card-glow"></div>
    
    <!-- 角色头像区域 -->
    <div class="character-avatar">
      <div class="avatar-frame">
        <img :src="character.portrait" :alt="character.name" class="avatar-image" />
        <div class="avatar-overlay"></div>
      </div>
      
      <!-- 等级标识 -->
      <div class="level-badge">
        <span>Lv.{{ character.level }}</span>
      </div>
      
      <!-- 状态标识 -->
      <div class="status-badge" :class="`status-${character.status.toLowerCase()}`">
        <span>{{ getStatusText(character.status) }}</span>
      </div>
    </div>
    
    <!-- 角色信息区域 -->
    <div class="character-info">
      <div class="character-name">{{ character.name }}</div>
      <div class="character-type">{{ getTypeText(character.type) }}</div>
      
      <!-- 属性条 -->
      <div class="character-stats">
        <div class="stat-bar">
          <div class="stat-label">
            <span>体力</span>
            <span class="stat-value">{{ character.stamina }}/100</span>
          </div>
          <div class="stat-progress">
            <div 
              class="stat-fill stamina-fill" 
              :style="{ width: `${character.stamina}%` }"
            ></div>
          </div>
        </div>
        
        <div class="stat-bar">
          <div class="stat-label">
            <span>心情</span>
            <span class="stat-value">{{ character.mood }}/100</span>
          </div>
          <div class="stat-progress">
            <div 
              class="stat-fill mood-fill" 
              :style="{ width: `${character.mood}%` }"
            ></div>
          </div>
        </div>
        
        <div class="stat-bar">
          <div class="stat-label">
            <span>经验</span>
            <span class="stat-value">{{ character.experience }}/100</span>
          </div>
          <div class="stat-progress">
            <div 
              class="stat-fill exp-fill" 
              :style="{ width: `${character.experience}%` }"
            ></div>
          </div>
        </div>
      </div>
      
      <!-- 技能预览 -->
      <div class="character-skills" v-if="character.attributes.skills.length > 0">
        <div class="skills-header">
          <span>技能</span>
          <a-button 
            type="text" 
            size="small" 
            @click.stop="showDetails = true"
            class="details-btn"
          >
            详情
          </a-button>
        </div>
        <div class="skills-preview">
          <a-tag 
            v-for="skillId in character.attributes.skills.slice(0, 2)" 
            :key="skillId"
            :color="getSkillColor(skillId)"
            size="small"
          >
            {{ getSkillName(skillId) }}
          </a-tag>
          <span v-if="character.attributes.skills.length > 2" class="more-skills">
            +{{ character.attributes.skills.length - 2 }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- 选择指示器 -->
    <div v-if="selectable" class="selection-indicator">
      <div class="selection-circle" :class="{ active: selected }">
        <CheckOutlined v-if="selected" />
      </div>
    </div>
    
    <!-- 派遣状态指示器 -->
    <div v-if="character.attributes.isAssigned" class="assigned-indicator">
      <span>派遣中</span>
    </div>
  </div>
  
  <!-- 详情弹窗 -->
  <a-modal 
    v-model:open="showDetails" 
    :title="`${character.name} - 详细信息`"
    :footer="null"
    width="500px"
    centered
  >
    <CharacterDetails :character="character" />
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CheckOutlined } from '@ant-design/icons-vue'
import type { Character } from '@/stores/characterStore'
import { useSkillStore } from '@/stores/skillStore'
import CharacterDetails from './CharacterDetails.vue'

interface Props {
  character: Character
  selected?: boolean
  selectable?: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'select', character: Character): void
  (e: 'click', character: Character): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  selectable: false,
  disabled: false
})

const emit = defineEmits<Emits>()

const skillStore = useSkillStore()
const showDetails = ref(false)

// 处理卡片点击
const handleCardClick = () => {
  if (props.disabled) return
  
  if (props.selectable) {
    emit('select', props.character)
  } else {
    emit('click', props.character)
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'Normal': '正常',
    'Tired': '疲惫',
    'Overworked': '过劳',
    'Resting': '休息',
    'Exiled': '流放'
  }
  return statusMap[status] || status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return typeMap[type] || type
}

// 获取技能名称
const getSkillName = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.name : skillId
}

// 获取技能颜色
const getSkillColor = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  if (!skill) return 'default'
  return skill.rarity === 'Rare' ? 'purple' : 'blue'
}
</script>

<style scoped>
.character-card {
  position: relative;
  width: 280px;
  height: 380px;
  background: linear-gradient(145deg, #ffffff, #f0f2f5);
  border-radius: 20px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  border: 2px solid transparent;
}

.character-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.character-card:hover::before {
  transform: translateX(100%);
}

.character-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 卡片类型样式 */
.character-normal {
  border-image: linear-gradient(45deg, #74b9ff, #0984e3) 1;
}

.character-rare {
  border-image: linear-gradient(45deg, #a29bfe, #6c5ce7) 1;
}

.character-special {
  border-image: linear-gradient(45deg, #fd79a8, #e84393) 1;
  animation: special-glow 2s ease-in-out infinite alternate;
}

@keyframes special-glow {
  from {
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.1),
      0 0 20px rgba(232, 67, 147, 0.3);
  }
  to {
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.1),
      0 0 30px rgba(232, 67, 147, 0.5);
  }
}

/* 状态样式 */
.status-tired {
  filter: saturate(0.7);
}

.status-overworked {
  filter: saturate(0.5) brightness(0.8);
}

.status-exiled {
  filter: grayscale(0.8);
  opacity: 0.7;
}

.card-selected {
  border-color: var(--nectar-purple);
  box-shadow: 
    0 8px 32px rgba(108, 92, 231, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.card-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.card-assigned {
  border-color: #52c41a;
}

/* 头像区域 */
.character-avatar {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.avatar-frame {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.character-card:hover .avatar-image {
  transform: scale(1.1);
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 30%, rgba(255, 255, 255, 0.2));
}

.level-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: linear-gradient(45deg, #ffd700, #ffb347);
  color: #333;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
}

.status-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: bold;
  color: white;
}

.status-normal { background: #52c41a; }
.status-tired { background: #faad14; }
.status-overworked { background: #ff4d4f; }
.status-resting { background: #1890ff; }
.status-exiled { background: #8c8c8c; }

/* 角色信息 */
.character-info {
  text-align: center;
}

.character-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.character-type {
  font-size: 12px;
  color: #666;
  margin-bottom: 16px;
}

/* 属性条 */
.character-stats {
  margin-bottom: 16px;
}

.stat-bar {
  margin-bottom: 8px;
}

.stat-label {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-progress {
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.stat-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.stamina-fill { background: linear-gradient(90deg, #52c41a, #73d13d); }
.mood-fill { background: linear-gradient(90deg, #1890ff, #40a9ff); }
.exp-fill { background: linear-gradient(90deg, #faad14, #ffc53d); }

/* 技能区域 */
.character-skills {
  text-align: left;
}

.skills-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.details-btn {
  padding: 0 4px;
  height: auto;
  font-size: 10px;
}

.skills-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.more-skills {
  font-size: 10px;
  color: #999;
}

/* 选择指示器 */
.selection-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
}

.selection-circle {
  width: 24px;
  height: 24px;
  border: 2px solid #d9d9d9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  transition: all 0.3s ease;
}

.selection-circle.active {
  border-color: var(--nectar-purple);
  background: var(--nectar-purple);
  color: white;
}

/* 派遣指示器 */
.assigned-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .character-card {
    width: 240px;
    height: 320px;
    padding: 16px;
  }
  
  .avatar-frame {
    width: 60px;
    height: 60px;
  }
  
  .character-name {
    font-size: 16px;
  }
}
</style>
