<template>
  <div class="workshop-container">
    <!-- 顶部导航 -->
    <div class="workshop-header">
      <a-button 
        type="text" 
        @click="goBack"
        class="back-btn"
      >
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回琉璃胭脂坊
      </a-button>
      
      <h1 class="workshop-title">{{ workshopInfo.name }}</h1>
      
      <div class="workshop-stats">
        <span class="available-slots">可用槽位: {{ availableSlots }}/{{ maxSlots }}</span>
      </div>
    </div>
    
    <!-- 工坊描述 -->
    <div class="workshop-description">
      <p>{{ workshopInfo.description }}</p>
      <div class="production-info" v-if="workshopInfo.type !== 'hall'">
        <span class="cost-info">消耗材料: {{ workshopInfo.materialCost }}</span>
        <span class="output-info">产出: {{ workshopInfo.output }}</span>
      </div>
    </div>
    
    <!-- 可派遣角色列表 -->
    <div class="available-characters">
      <h3>可派遣角色</h3>
      <div class="character-grid">
        <div 
          v-for="character in characterStore.availableCharacters" 
          :key="character.id"
          class="character-card"
          :class="{ selected: selectedCharacters.includes(character.id) }"
          @click="toggleCharacterSelection(character.id)"
        >
          <a-avatar 
            :src="`/src/assets/graphics/portraits/${character.portrait}`"
            :size="64"
            class="character-avatar"
          />
          <div class="character-info">
            <h4>{{ character.name }}</h4>
            <a-tag :color="getTypeColor(character.type)" size="small">
              {{ character.type }}
            </a-tag>
            <div class="character-stats">
              <span class="stat">效率: {{ character.attributes.efficiency }}</span>
              <span class="stat">体力: {{ character.stamina }}/100</span>
              <span class="stat">心情: {{ character.mood }}/100</span>
            </div>
            <div class="character-skills">
              <a-tag 
                v-for="skill in character.attributes.skills" 
                :key="skill"
                size="small"
                color="blue"
              >
                {{ skill }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="characterStore.availableCharacters.length === 0" class="no-characters">
        <a-empty description="暂无可派遣的角色" />
      </div>
    </div>
    
    <!-- 派遣槽位 -->
    <div class="dispatch-slots">
      <h3>派遣槽位</h3>
      <div class="slots-grid">
        <div 
          v-for="(slot, index) in dispatchSlots" 
          :key="index"
          class="dispatch-slot"
          :class="{ occupied: slot.characterId }"
        >
          <div v-if="slot.characterId" class="assigned-character">
            <a-avatar 
              :src="`/src/assets/graphics/portraits/${getCharacterById(slot.characterId)?.portrait}`"
              :size="48"
              class="character-avatar"
            />
            <span class="character-name">{{ getCharacterById(slot.characterId)?.name }}</span>
            <a-button 
              size="small" 
              type="text" 
              @click="removeFromSlot(index)"
              class="remove-btn"
            >
              <template #icon>
                <CloseOutlined />
              </template>
            </a-button>
          </div>
          <div v-else class="empty-slot">
            <PlusOutlined class="add-icon" />
            <span>点击角色卡片添加到此槽位</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 派遣预览和确认 -->
    <div class="dispatch-preview" v-if="hasSelectedCharacters">
      <h3>派遣预览</h3>
      <div class="preview-content">
        <div class="cost-preview">
          <h4>消耗预测</h4>
          <div class="cost-items">
            <span v-if="workshopInfo.type !== 'hall'">材料: {{ totalMaterialCost }}</span>
            <span>角色体力: {{ totalStaminaCost }}</span>
            <span>角色心情: {{ totalMoodCost }}</span>
          </div>
        </div>
        
        <div class="output-preview">
          <h4>产出预测</h4>
          <div class="output-items">
            <span v-if="workshopInfo.type === 'hall'">金币: +{{ predictedGoldOutput }}</span>
            <span v-else>{{ workshopInfo.output }}: +{{ predictedOutput }}</span>
            <span>角色经验: +{{ predictedExperience }}</span>
          </div>
        </div>
      </div>
      
      <div class="dispatch-actions">
        <a-button @click="clearSelection">清空选择</a-button>
        <a-button 
          type="primary" 
          @click="confirmDispatch"
          :loading="dispatching"
          :disabled="!canDispatch"
        >
          确认派遣
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined, PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { useGameStore } from '@/stores/gameStore'
import { useCharacterStore, type Character } from '@/stores/characterStore'

const router = useRouter()
const route = useRoute()
const gameStore = useGameStore()
const characterStore = useCharacterStore()

// 工坊类型
const workshopType = computed(() => route.params.type as string)

// 工坊信息配置
const workshopConfigs = {
  hall: {
    name: '会客大厅',
    description: '让少女们进行歌舞曲艺表演，获得金币收入',
    type: 'hall',
    materialCost: '无',
    output: '金币',
    maxSlots: 2
  },
  'holy-water': {
    name: '圣水工坊',
    description: '生产圣水，用于农场浇水和其他用途',
    type: 'production',
    materialCost: '种子 x2',
    output: '圣水',
    maxSlots: 2
  },
  honey: {
    name: '蜜酿工坊',
    description: '生产蜜酿，珍贵的材料和商品',
    type: 'production',
    materialCost: '灵果 x1',
    output: '蜜酿',
    maxSlots: 2
  },
  milk: {
    name: '乳液工坊',
    description: '生产乳液，高级材料和商品',
    type: 'production',
    materialCost: '圣水 x2, 蜜酿 x1',
    output: '乳液',
    maxSlots: 2
  },
  nectar: {
    name: '琼浆工坊',
    description: '生产琼浆，最珍贵的产品',
    type: 'production',
    materialCost: '乳液 x2, 蜜酿 x2',
    output: '琼浆',
    maxSlots: 2
  }
}

// 当前工坊信息
const workshopInfo = computed(() => {
  return workshopConfigs[workshopType.value as keyof typeof workshopConfigs] || workshopConfigs.hall
})

// 派遣相关状态
const selectedCharacters = ref<string[]>([])
const dispatchSlots = ref<Array<{ characterId: string | null }>>([])
const dispatching = ref(false)

// 计算属性
const maxSlots = computed(() => workshopInfo.value.maxSlots)
const availableSlots = computed(() => {
  return dispatchSlots.value.filter(slot => !slot.characterId).length
})

const hasSelectedCharacters = computed(() => {
  return dispatchSlots.value.some(slot => slot.characterId)
})

const canDispatch = computed(() => {
  return hasSelectedCharacters.value && !dispatching.value
})

// 预测计算
const totalMaterialCost = computed(() => {
  const assignedCount = dispatchSlots.value.filter(slot => slot.characterId).length
  return assignedCount * 2 // 简化计算
})

const totalStaminaCost = computed(() => {
  return dispatchSlots.value.filter(slot => slot.characterId).length * 15
})

const totalMoodCost = computed(() => {
  return dispatchSlots.value.filter(slot => slot.characterId).length * 10
})

const predictedOutput = computed(() => {
  let total = 0
  dispatchSlots.value.forEach(slot => {
    if (slot.characterId) {
      const character = characterStore.getCharacterById(slot.characterId)
      if (character) {
        total += Math.floor(character.attributes.efficiency * 10)
      }
    }
  })
  return total
})

const predictedGoldOutput = computed(() => {
  return predictedOutput.value * 50 // 会客大厅金币产出
})

const predictedExperience = computed(() => {
  return dispatchSlots.value.filter(slot => slot.characterId).length * 20
})

// 方法
const goBack = () => {
  router.push('/llyzf')
}

const getTypeColor = (type: string) => {
  const colors = {
    Normal: 'default',
    Rare: 'blue',
    Special: 'purple'
  }
  return colors[type as keyof typeof colors] || 'default'
}

const getCharacterById = (id: string): Character | undefined => {
  return characterStore.getCharacterById(id)
}

const toggleCharacterSelection = (characterId: string) => {
  // 找到第一个空槽位
  const emptySlotIndex = dispatchSlots.value.findIndex(slot => !slot.characterId)
  
  if (emptySlotIndex !== -1) {
    // 检查角色是否已经在其他槽位
    const existingSlotIndex = dispatchSlots.value.findIndex(slot => slot.characterId === characterId)
    
    if (existingSlotIndex !== -1) {
      // 如果已存在，则移除
      dispatchSlots.value[existingSlotIndex].characterId = null
    } else {
      // 添加到空槽位
      dispatchSlots.value[emptySlotIndex].characterId = characterId
    }
  } else {
    message.warning('所有槽位已满')
  }
}

const removeFromSlot = (slotIndex: number) => {
  dispatchSlots.value[slotIndex].characterId = null
}

const clearSelection = () => {
  dispatchSlots.value.forEach(slot => {
    slot.characterId = null
  })
  selectedCharacters.value = []
}

const confirmDispatch = async () => {
  dispatching.value = true
  
  try {
    // 模拟派遣过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 处理派遣逻辑
    const assignedCharacters: string[] = []
    
    dispatchSlots.value.forEach(slot => {
      if (slot.characterId) {
        const success = characterStore.assignCharacter(slot.characterId, 'Producing')
        if (success) {
          assignedCharacters.push(slot.characterId)
          // 消耗角色状态
          characterStore.consumeCharacterStats(slot.characterId, 15, 10)
        }
      }
    })
    
    if (assignedCharacters.length > 0) {
      // 消耗材料（如果需要）
      if (workshopInfo.value.type !== 'hall') {
        // 这里应该调用gameStore的消耗材料方法
        // gameStore.consumeMaterial(...)
      }
      
      // 增加产出
      if (workshopInfo.value.type === 'hall') {
        gameStore.addMoney(predictedGoldOutput.value)
      } else {
        // gameStore.addMaterial(...)
      }
      
      // 增加经验
      assignedCharacters.forEach(characterId => {
        characterStore.addExperience(characterId, 20)
      })
      
      // 保存数据
      characterStore.saveCharacters()
      gameStore.saveGame()
      
      message.success(`成功派遣${assignedCharacters.length}名角色`)
      
      // 推进时间并返回地图
      setTimeout(() => {
        const result = gameStore.advanceTime()
        message.info(result.message)
        router.push('/main')
      }, 1000)
    } else {
      message.error('派遣失败')
    }
  } catch (error) {
    message.error('派遣过程中发生错误')
  } finally {
    dispatching.value = false
  }
}

// 初始化
onMounted(() => {
  // 初始化槽位
  dispatchSlots.value = Array(maxSlots.value).fill(null).map(() => ({ characterId: null }))
  
  // 检查工坊是否存在
  if (!workshopConfigs[workshopType.value as keyof typeof workshopConfigs]) {
    message.error('未知的工坊类型')
    router.push('/llyzf')
    return
  }
  
  // 检查时间段
  if (gameStore.currentTimeSlot !== '上午') {
    message.warning('只能在上午时段进行派遣')
    router.push('/llyzf')
    return
  }
  
  message.success(`进入${workshopInfo.value.name}`)
})
</script>

<style scoped>
.workshop-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.workshop-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.workshop-title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: var(--nectar-purple);
}

.available-slots {
  font-size: 14px;
  color: #666;
}

.workshop-description {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.production-info {
  margin-top: 12px;
  display: flex;
  gap: 20px;
}

.cost-info {
  color: #e74c3c;
  font-size: 14px;
}

.output-info {
  color: #27ae60;
  font-size: 14px;
}

.available-characters {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.character-card {
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.character-card:hover {
  border-color: var(--nectar-purple);
  background: white;
}

.character-card.selected {
  border-color: var(--nectar-purple);
  background: #f0f5ff;
}

.character-info h4 {
  margin: 8px 0 4px 0;
  color: #333;
}

.character-stats {
  display: flex;
  gap: 8px;
  margin: 8px 0;
  flex-wrap: wrap;
}

.stat {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.character-skills {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.dispatch-slots {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.dispatch-slot {
  border: 2px dashed var(--nectar-gold);
  border-radius: 12px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: rgba(212, 175, 55, 0.1);
}

.dispatch-slot.occupied {
  border-style: solid;
  background: white;
}

.assigned-character {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4d4f;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
  text-align: center;
}

.add-icon {
  font-size: 24px;
}

.dispatch-preview {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 16px 0;
}

.cost-preview, .output-preview {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.cost-items, .output-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 8px;
}

.cost-items span {
  color: #e74c3c;
  font-size: 14px;
}

.output-items span {
  color: #27ae60;
  font-size: 14px;
}

.dispatch-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.no-characters {
  text-align: center;
  padding: 40px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .workshop-container {
    padding: 16px;
  }
  
  .workshop-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .character-grid {
    grid-template-columns: 1fr;
  }
  
  .slots-grid {
    grid-template-columns: 1fr;
  }
  
  .preview-content {
    grid-template-columns: 1fr;
  }
  
  .dispatch-actions {
    flex-direction: column;
  }
}
</style>
