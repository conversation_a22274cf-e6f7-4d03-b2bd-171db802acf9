import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import charactersNameData from '@/assets/data/characters/NameList.json'
import portraitsData from '@/assets/graphics/portraits/characters_portraits.json'

export type CharacterType = 'Normal' | 'Rare' | 'Special'
export type ActivityType = 'Idle' | 'Producing' | 'Performing'

export interface Character {
  id: string
  name: string
  portrait: string
  type: CharacterType
  attributes: {
    energy: number
    maxEnergy: number
    isAssigned: boolean
    skills: string[]
    efficiency: number // 生产效率 0.5-2.0
  }
  currentActivity: ActivityType
  mood: number // 心情值 0-100
  stamina: number // 体力值 0-100
  experience: number // 经验值
  level: number // 等级
}

export const useCharacterStore = defineStore('character', () => {
  // 角色列表
  const characters = ref<Character[]>([])
  
  // 已使用的名字列表
  const usedNames = ref<Set<string>>(new Set())
  
  // 计算属性：可用角色（未被派遣的）
  const availableCharacters = computed(() => {
    return characters.value.filter(char => !char.attributes.isAssigned)
  })
  
  // 计算属性：按类型分组的角色
  const charactersByType = computed(() => {
    return {
      Normal: characters.value.filter(char => char.type === 'Normal'),
      Rare: characters.value.filter(char => char.type === 'Rare'),
      Special: characters.value.filter(char => char.type === 'Special'),
    }
  })
  
  // 生成随机角色ID
  function generateCharacterId(): string {
    return 'char_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }
  
  // 获取随机未使用的名字
  function getRandomUnusedName(): string {
    const availableNames = charactersNameData.charactersName.filter(
      name => !usedNames.value.has(name)
    )
    
    if (availableNames.length === 0) {
      // 如果所有名字都用完了，重置已使用名字列表
      usedNames.value.clear()
      return charactersNameData.charactersName[
        Math.floor(Math.random() * charactersNameData.charactersName.length)
      ]
    }
    
    const randomName = availableNames[Math.floor(Math.random() * availableNames.length)]
    usedNames.value.add(randomName)
    return randomName
  }
  
  // 获取随机头像
  function getRandomPortrait(): string {
    const randomIndex = Math.floor(Math.random() * portraitsData.length)
    return portraitsData[randomIndex]
  }
  
  // 生成角色属性
  function generateCharacterAttributes(type: CharacterType) {
    let baseEfficiency = 1.0
    let baseEnergy = 100
    let baseMood = 70
    let baseStamina = 80
    
    switch (type) {
      case 'Rare':
        baseEfficiency = 1.2 + Math.random() * 0.3 // 1.2-1.5
        baseEnergy = 120
        baseMood = 80
        baseStamina = 90
        break
      case 'Special':
        baseEfficiency = 1.5 + Math.random() * 0.5 // 1.5-2.0
        baseEnergy = 150
        baseMood = 90
        baseStamina = 100
        break
      default: // Normal
        baseEfficiency = 0.8 + Math.random() * 0.4 // 0.8-1.2
        break
    }
    
    return {
      energy: baseEnergy,
      maxEnergy: baseEnergy,
      isAssigned: false,
      skills: generateRandomSkills(type),
      efficiency: Math.round(baseEfficiency * 100) / 100
    }
  }
  
  // 生成随机技能
  function generateRandomSkills(type: CharacterType): string[] {
    const allSkills = [
      '圣水制作', '蜜酿制作', '乳液制作', '琼浆制作',
      '歌舞表演', '魅力加成', '效率提升', '体力恢复'
    ]
    
    let skillCount = 1
    if (type === 'Rare') skillCount = 2
    if (type === 'Special') skillCount = 3
    
    const skills: string[] = []
    const availableSkills = [...allSkills]
    
    for (let i = 0; i < skillCount; i++) {
      if (availableSkills.length === 0) break
      const randomIndex = Math.floor(Math.random() * availableSkills.length)
      skills.push(availableSkills.splice(randomIndex, 1)[0])
    }
    
    return skills
  }
  
  // 创建新角色
  function createCharacter(type: CharacterType = 'Normal'): Character {
    const character: Character = {
      id: generateCharacterId(),
      name: getRandomUnusedName(),
      portrait: getRandomPortrait(),
      type,
      attributes: generateCharacterAttributes(type),
      currentActivity: 'Idle',
      mood: 70 + Math.random() * 20, // 70-90
      stamina: 80 + Math.random() * 20, // 80-100
      experience: 0,
      level: 1
    }
    
    characters.value.push(character)
    return character
  }
  
  // 根据ID查找角色
  function getCharacterById(id: string): Character | undefined {
    return characters.value.find(char => char.id === id)
  }
  
  // 派遣角色
  function assignCharacter(characterId: string, activity: ActivityType): boolean {
    const character = getCharacterById(characterId)
    if (!character || character.attributes.isAssigned) {
      return false
    }
    
    character.attributes.isAssigned = true
    character.currentActivity = activity
    return true
  }
  
  // 取消派遣
  function unassignCharacter(characterId: string): boolean {
    const character = getCharacterById(characterId)
    if (!character) return false
    
    character.attributes.isAssigned = false
    character.currentActivity = 'Idle'
    return true
  }
  
  // 恢复角色体力和心情
  function restoreCharacter(characterId: string, stamina: number = 20, mood: number = 10) {
    const character = getCharacterById(characterId)
    if (!character) return false
    
    character.stamina = Math.min(character.stamina + stamina, 100)
    character.mood = Math.min(character.mood + mood, 100)
    return true
  }
  
  // 消耗角色体力和心情
  function consumeCharacterStats(characterId: string, stamina: number = 10, mood: number = 5) {
    const character = getCharacterById(characterId)
    if (!character) return false
    
    character.stamina = Math.max(character.stamina - stamina, 0)
    character.mood = Math.max(character.mood - mood, 0)
    return true
  }
  
  // 增加角色经验
  function addExperience(characterId: string, exp: number) {
    const character = getCharacterById(characterId)
    if (!character) return false
    
    character.experience += exp
    
    // 检查是否升级
    const expNeeded = character.level * 100
    if (character.experience >= expNeeded) {
      character.level += 1
      character.experience -= expNeeded
      character.attributes.maxEnergy += 10
      character.attributes.efficiency += 0.1
      return true // 返回true表示升级了
    }
    
    return false
  }
  
  // 初始化默认角色
  function initializeDefaultCharacters() {
    if (characters.value.length === 0) {
      // 创建3个初始角色
      createCharacter('Normal')
      createCharacter('Normal')
      createCharacter('Rare')
    }
  }
  
  // 保存角色数据
  function saveCharacters() {
    const data = {
      characters: characters.value,
      usedNames: Array.from(usedNames.value)
    }
    localStorage.setItem('nectar-game-characters', JSON.stringify(data))
  }
  
  // 加载角色数据
  function loadCharacters(): boolean {
    try {
      const saved = localStorage.getItem('nectar-game-characters')
      if (saved) {
        const data = JSON.parse(saved)
        characters.value = data.characters || []
        usedNames.value = new Set(data.usedNames || [])
        return true
      }
    } catch (error) {
      console.error('Failed to load characters:', error)
    }
    return false
  }
  
  // 重置角色数据
  function resetCharacters() {
    characters.value = []
    usedNames.value.clear()
    localStorage.removeItem('nectar-game-characters')
    initializeDefaultCharacters()
  }
  
  return {
    // 状态
    characters,
    usedNames,
    
    // 计算属性
    availableCharacters,
    charactersByType,
    
    // 方法
    createCharacter,
    getCharacterById,
    assignCharacter,
    unassignCharacter,
    restoreCharacter,
    consumeCharacterStats,
    addExperience,
    initializeDefaultCharacters,
    saveCharacters,
    loadCharacters,
    resetCharacters,
  }
})
